# Lapped AI 文档系统

这是 Lapped AI 项目的官方文档系统，基于 Nextra 构建。

## 🚀 快速开始

### 安装依赖

```bash
cd docs
npm install
```

### 开发模式

```bash
npm run dev
```

文档将在 [http://localhost:3000](http://localhost:3000) 启动。

### 构建生产版本

```bash
npm run build
npm start
```

### 导出静态文件

```bash
npm run export
```

## 📁 项目结构

```
docs/
├── pages/           # 文档页面
│   ├── _meta.json   # 导航配置
│   ├── index.mdx    # 首页
│   ├── getting-started.mdx
│   └── features.mdx
├── styles/          # 样式文件
├── next.config.mjs  # Next.js 配置
├── theme.config.tsx # Nextra 主题配置
└── package.json
```

## 📝 添加新页面

1. 在 `pages/` 目录下创建新的 `.mdx` 文件
2. 更新 `pages/_meta.json` 添加导航链接
3. 使用 Markdown 语法编写内容

## 🎨 自定义主题

编辑 `theme.config.tsx` 文件来自定义文档主题：

- 修改 logo 和品牌信息
- 调整颜色主题
- 配置导航和页脚

## 🔧 配置选项

### Nextra 配置

在 `next.config.mjs` 中可以配置：

- 主题设置
- MDX 选项
- 插件配置

### 主题配置

在 `theme.config.tsx` 中可以配置：

- 网站信息
- 导航结构
- 样式主题

## 📚 更多信息

- [Nextra 官方文档](https://nextra.site)
- [Nextra Theme Docs](https://nextra.site/docs)
- [MDX 语法指南](https://mdxjs.com) 