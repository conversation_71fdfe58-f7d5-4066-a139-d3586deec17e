#!/bin/bash

echo "🚀 开始设置 Lapped AI 文档系统..."

# 检查 Node.js 版本
echo "📋 检查 Node.js 版本..."
node_version=$(node -v)
echo "当前 Node.js 版本: $node_version"

# 安装依赖
echo "📦 安装依赖..."
npm install

# 检查安装是否成功
if [ $? -eq 0 ]; then
    echo "✅ 依赖安装成功！"
else
    echo "❌ 依赖安装失败，请检查错误信息"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p pages
mkdir -p styles

echo "✅ 设置完成！"
echo ""
echo "🎯 下一步："
echo "1. 运行 'npm run dev' 启动开发服务器"
echo "2. 访问 http://localhost:3000 查看文档"
echo "3. 编辑 pages/ 目录下的文件来修改内容"
echo ""
echo "�� 更多信息请查看 README.md" 