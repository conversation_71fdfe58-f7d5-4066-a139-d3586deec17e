# 部署指南

本文档介绍如何部署 Lapped AI 文档系统。

## 🚀 本地开发

### 1. 安装依赖

```bash
# 安装文档系统依赖
cd docs
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

文档将在 [http://localhost:3000](http://localhost:3000) 启动。

## 🌐 生产部署

### Vercel 部署（推荐）

1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 设置构建配置：
   - **Framework Preset**: Next.js
   - **Root Directory**: `docs`
   - **Build Command**: `npm run build`
   - **Output Directory**: `.next`

### Netlify 部署

1. 构建项目：
   ```bash
   cd docs
   npm run build
   npm run export
   ```

2. 将 `out` 目录部署到 Netlify

### Docker 部署

创建 `Dockerfile`：

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

构建和运行：

```bash
docker build -t lapped-docs .
docker run -p 3000:3000 lapped-docs
```

## 🔧 环境配置

### 环境变量

创建 `.env.local` 文件：

```env
NEXT_PUBLIC_SITE_URL=https://your-domain.com
NEXT_PUBLIC_GA_ID=your-google-analytics-id
```

### 自定义域名

1. 在 DNS 提供商处添加 CNAME 记录
2. 在部署平台配置自定义域名
3. 更新 `theme.config.tsx` 中的项目链接

## 📊 性能优化

### 图片优化

- 使用 Next.js Image 组件
- 压缩图片文件
- 使用 WebP 格式

### 代码分割

- 使用动态导入
- 配置页面预加载
- 优化包大小

## 🔒 安全配置

### HTTPS

确保生产环境使用 HTTPS：

```javascript
// next.config.mjs
export default withNextra({
  // 强制 HTTPS
  headers: async () => [
    {
      source: '/(.*)',
      headers: [
        {
          key: 'Strict-Transport-Security',
          value: 'max-age=31536000; includeSubDomains'
        }
      ]
    }
  ]
})
```

### 内容安全策略

```javascript
// next.config.mjs
export default withNextra({
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"
          }
        ]
      }
    ]
  }
})
```

## 📈 监控和分析

### Google Analytics

在 `theme.config.tsx` 中配置：

```typescript
export default {
  // ... 其他配置
  analytics: {
    googleAnalytics: {
      measurementId: 'G-XXXXXXXXXX'
    }
  }
}
```

### 错误监控

集成 Sentry 或其他错误监控服务。

## 🔄 持续集成

### GitHub Actions

创建 `.github/workflows/deploy.yml`：

```yaml
name: Deploy Docs

on:
  push:
    branches: [main]
    paths: ['docs/**']

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: |
          cd docs
          npm ci
          npm run build
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          working-directory: ./docs
```

## 📞 支持

如果遇到部署问题，请：

1. 查看部署平台日志
2. 检查环境配置
3. 联系技术支持团队 