import React from 'react'

export default {
  logo: <span>Lapped AI 文档</span>,
  project: {
    link: 'https://github.com/your-username/lapped-web',
  },
  docsRepositoryBase: 'https://github.com/your-username/lapped-web/tree/main/docs',
  footer: {
    text: '© 2025 沈阳帧译科技有限公司',
  },
  head: (
    <>
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta property="og:title" content="Lapped AI 文档" />
      <meta property="og:description" content="Lapped AI 智能写作助手官方文档" />
    </>
  ),
  useNextSeoProps() {
    return {
      titleTemplate: '%s – Lapped AI 文档'
    }
  },
  primaryHue: {
    dark: 200,
    light: 200,
  },
  primarySaturation: {
    dark: 100,
    light: 100,
  },
} 