import type { Metadata } from "next";
import localFont from "next/font/local";
import "./globals.css";
import { Toaster } from 'sonner'
import { Header } from "@/components/header"
import { AuthProvider } from '@/contexts/auth-context'
import Script from 'next/script'
import Link from 'next/link'

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: "Lapped AI官方网站",
  description: "Lapped AI是一款智能写作助手，帮助你更高效地创作内容",
  icons: {
    icon: '/lapped.ico',
    apple: '/lapped.ico',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh">
      <head>
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-72ER7JTZBE"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-72ER7JTZBE');
          `}
        </Script>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        <AuthProvider>
          <Header />
          <main className="mt-20 flex-1">
            {children}
          </main>
          <footer className="w-full py-4 border-t">
            <div className="container mx-auto px-4 text-center text-sm text-gray-600">
              <Link 
                href="https://beian.mps.gov.cn" 
                target="_blank" 
                rel="noopener noreferrer"
                className="hover:text-gray-900"
              >
                Copyright© 2025 沈阳帧译科技有限公司 辽公网安备21010502000850号
              </Link>
            </div>
          </footer>
        </AuthProvider>
        <Toaster position="top-center" />
      </body>
    </html>
  );
}
